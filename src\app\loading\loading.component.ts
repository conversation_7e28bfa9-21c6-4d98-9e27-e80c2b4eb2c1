import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from '../../services/auth0.service';
import { take } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss'],
})
export class LoadingComponent implements OnInit {
  token: any;
  queryParam: any;
  subscriptionId: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private auth0: Auth0Service,
    private router: Router,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    console.log('[LoadingComponent] ngOnInit');

    // Get query parameters
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.queryParam = params;
      this.subscriptionId = params['sub'] || localStorage.getItem('SubscriptionID');

      console.log('Query params:', params);
      console.log('Subscription ID:', this.subscriptionId);

      // Handle Auth0 authentication state
      this.handleAuthentication();
    });
  }

  private handleAuthentication(): void {
    // Check if user is authenticated
    this.auth.isAuthenticated$.pipe(take(1)).subscribe((isAuthenticated) => {
      console.log('Is authenticated:', isAuthenticated);

      if (isAuthenticated) {
        // User is authenticated, handle the flow
        this.handleAuthenticatedUser();
      } else {
        // User is not authenticated, redirect to login
        console.log('User not authenticated, redirecting to login');
        this.auth0.login();
      }
    });
  }

  private handleAuthenticatedUser(): void {
    // Get user profile and token
    this.auth.user$.pipe(take(1)).subscribe((user) => {
      console.log('User profile:', user);
      if (user) {
        localStorage.setItem('user', JSON.stringify(user));
      }
    });

    // Get access token
    this.auth.getAccessTokenSilently().subscribe({
      next: (token) => {
        console.log('Access token obtained successfully');
        this.token = token;

        // If we have a subscription ID, proceed to home
        if (this.subscriptionId) {
          localStorage.setItem('SubscriptionID', this.subscriptionId);
          console.log('Redirecting to home with subscription ID:', this.subscriptionId);
          this.router.navigate(['/home'], { queryParams: { sub: this.subscriptionId } });
        } else {
          // No subscription ID, stay on loading page or redirect to error
          console.log('No subscription ID found');
          // You might want to show an error message or redirect to a different page
        }
      },
      error: (error) => {
        console.error('Error getting access token:', error);
        // Handle token error - might need to re-authenticate
        this.auth0.login();
      }
    });
  }
}
