import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from '../../services/auth0.service';
import { take } from 'rxjs/operators';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-loading',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss'],
})
export class LoadingComponent implements OnInit {
  token: any;
  queryParam: any;
  subscriptionId: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private auth0: Auth0Service,
    private router: Router,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    console.log('📥 [LoadingComponent] ngOnInit called');
    console.log('📥 [LoadingComponent] Current URL:', window.location.href);
    console.log('📥 [LoadingComponent] Router URL:', this.router.url);

    // Check localStorage state
    const existingUser = localStorage.getItem('user');
    const existingSubId = localStorage.getItem('SubscriptionID');
    console.log('📥 [LoadingComponent] Existing localStorage user:', existingUser);
    console.log('📥 [LoadingComponent] Existing localStorage SubscriptionID:', existingSubId);

    // Get query parameters
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      console.log('📥 [LoadingComponent] Query params received:', params);
      this.queryParam = params;
      this.subscriptionId = params['sub'] || localStorage.getItem('SubscriptionID');

      console.log('📥 [LoadingComponent] Final subscription ID:', this.subscriptionId);
      console.log('📥 [LoadingComponent] All query params:', this.queryParam);

      // Handle Auth0 authentication state
      this.handleAuthentication();
    });
  }

  private handleAuthentication(): void {
    console.log('📥 [LoadingComponent] handleAuthentication called');

    // First check Auth0 loading state
    this.auth.isLoading$.pipe(take(1)).subscribe((isLoading) => {
      console.log('📥 [LoadingComponent] Auth0 isLoading:', isLoading);
    });

    // Check Auth0 error state
    this.auth.error$.pipe(take(1)).subscribe((error) => {
      if (error) {
        console.error('📥 [LoadingComponent] Auth0 error:', error);
      }
    });

    // Check if user is authenticated
    this.auth.isAuthenticated$.pipe(take(1)).subscribe((isAuthenticated) => {
      console.log('📥 [LoadingComponent] Auth0 isAuthenticated:', isAuthenticated);

      if (isAuthenticated) {
        console.log('📥 [LoadingComponent] User is authenticated, proceeding to handleAuthenticatedUser');
        // User is authenticated, handle the flow
        this.handleAuthenticatedUser();
      } else {
        console.log('📥 [LoadingComponent] User not authenticated, redirecting to login');
        console.log('📥 [LoadingComponent] Calling auth0.login()');
        this.auth0.login();
      }
    });
  }

  private handleAuthenticatedUser(): void {
    console.log('📥 [LoadingComponent] handleAuthenticatedUser called');

    // Get user profile and token
    this.auth.user$.pipe(take(1)).subscribe((user) => {
      console.log('📥 [LoadingComponent] Auth0 user profile:', user);
      if (user) {
        console.log('📥 [LoadingComponent] Storing user in localStorage');
        localStorage.setItem('user', JSON.stringify(user));
      } else {
        console.log('📥 [LoadingComponent] No user profile received');
      }
    });

    // Get access token
    console.log('📥 [LoadingComponent] Attempting to get access token silently');
    this.auth.getAccessTokenSilently().subscribe({
      next: (token) => {
        console.log('📥 [LoadingComponent] Access token obtained successfully');
        console.log('📥 [LoadingComponent] Token length:', token?.length);
        console.log('📥 [LoadingComponent] Token preview:', token?.substring(0, 50) + '...');
        this.token = token;

        // If we have a subscription ID, proceed to home
        if (this.subscriptionId) {
          console.log('📥 [LoadingComponent] Subscription ID found, storing and redirecting');
          localStorage.setItem('SubscriptionID', this.subscriptionId);
          console.log('📥 [LoadingComponent] Redirecting to /home with sub:', this.subscriptionId);
          this.router.navigate(['/home'], { queryParams: { sub: this.subscriptionId } });
        } else {
          console.log('📥 [LoadingComponent] No subscription ID found - staying on loading page');
          console.log('📥 [LoadingComponent] Available query params:', this.queryParam);
          // You might want to show an error message or redirect to a different page
        }
      },
      error: (error) => {
        console.error('📥 [LoadingComponent] Error getting access token:', error);
        console.error('📥 [LoadingComponent] Error details:', {
          message: error.message,
          error: error.error,
          error_description: error.error_description
        });
        // Handle token error - might need to re-authenticate
        console.log('📥 [LoadingComponent] Token error - redirecting to login');
        this.auth0.login();
      }
    });
  }
}
