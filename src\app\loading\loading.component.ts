import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Params, Router } from '@angular/router';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from '../../services/auth0.service';

@Component({
  selector: 'app-loading',
  templateUrl: './loading.component.html',
  styleUrls: ['./loading.component.scss'],
})
export class LoadingComponent implements OnInit {
  token: any;
  queryParam: any;
  subscriptionId: any;

  constructor(
    private activatedRoute: ActivatedRoute,
    private auth0: Auth0Service,
    private router: Router,
    private auth: AuthService
  ) {}

  ngOnInit(): void {
    this.subscriptionId = localStorage.getItem('SubscriptionID');
    this.activatedRoute.queryParams.subscribe((params: Params) => {
      this.queryParam = params;
    });
  }
}
