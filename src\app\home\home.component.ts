import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NavigationModule } from 'cax-design-system/navigation';
import { AuthService } from '@auth0/auth0-angular';
import { HttpClient } from '@angular/common/http';
import { UserService } from '../../services/user.service';
import { ProductsService } from '../../services/products.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    NavigationModule,

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {

  // Navigation data
  topNavList = [
    { label: 'Dashboard', icon: 'cax-dashboard' },
    { label: 'Products', icon: 'cax-widget' },
    { label: 'Analytics', icon: 'cax-chart' },
    { label: 'Settings', icon: 'cax-settings' }
  ];

  bottomNavList = [
    { label: 'Help', icon: 'cax-help' },
    { label: 'Profile', icon: 'cax-user' }
  ];

  // User data
  userName = 'User';
  userImage = 'https://primefaces.org/cdn/primeng/images/demo/avatar/walter.jpg';
  notificationCount = 6;
  version = '1.0.0';
  activeTab: { position: 'top' | 'bottom', index: number } = { position: 'top', index: 0 };
  localizedGreeting: string = '';

  // API data
  userProfile: any = null;
  productHeaders: any[] = [];
  apiUsageData: any = null;
  subscriptionId: string | null = null;
  isLoading = false;

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private auth: AuthService,
    private http: HttpClient,
    private userService: UserService,
    private productsService: ProductsService
  ) {}

  // Example: Simulate selecting a subscription and navigating to loader
  selectSubscriptionAndGoToLoader(subscriptionId: string) {
    localStorage.setItem('SubscriptionID', subscriptionId);
    this.router.navigate(['/loading'], { queryParams: { sub: subscriptionId } });
  }

  ngOnInit(): void {

    // Get subscription ID from query params or localStorage
    this.activatedRoute.queryParams.subscribe(params => {
      this.subscriptionId = params['sub'] || localStorage.getItem('SubscriptionID');
      if (this.subscriptionId) {
        this.loadData();
      }
    });

    // Update user info from Auth0
    this.auth.user$.subscribe(user => {
      if (user) {
        this.userName = user.name || user.nickname || 'User';
        this.userProfile = user;
      }
    });

    this.localizedGreeting = `Welcome, ${this.userName} to the localized home page!`;
  }

  private loadData(): void {
    if (!this.subscriptionId) return;

    this.isLoading = true;

    // Load user profile data
    this.userService.me(this.subscriptionId).subscribe({
      next: (response) => {
        this.userProfile = response.result;
        this.userName = this.userProfile.name || this.userName;
      },
      error: (error) => {
        console.error('Error loading user profile:', error);
      }
    });

    // Load product headers
    this.productsService.getProductHeader(this.subscriptionId).subscribe({
      next: (response) => {
        this.productHeaders = response.result || [];
      },
      error: (error) => {
        console.error('Error loading product headers:', error);
      }
    });

    // Load API usage data
    this.userService.apiUsage(this.subscriptionId, 'monthly').subscribe({
      next: (response) => {
        this.apiUsageData = response.result;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading API usage:', error);
        this.isLoading = false;
      }
    });
  }

}
