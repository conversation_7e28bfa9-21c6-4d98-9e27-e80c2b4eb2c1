import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NavigationModule } from 'cax-design-system/navigation';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    NavigationModule,

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {

  // Navigation data
  topNavList = [
    { label: 'Dashboard', icon: 'cax-dashboard' },
    { label: 'Products', icon: 'cax-widget' },
    { label: 'Analytics', icon: 'cax-chart' },
    { label: 'Settings', icon: 'cax-settings' }
  ];

  bottomNavList = [
    { label: 'Help', icon: 'cax-help' },
    { label: 'Profile', icon: 'cax-user' }
  ];

  // User data
  userName = '<PERSON>';
  userImage = 'https://primefaces.org/cdn/primeng/images/demo/avatar/walter.jpg';
  notificationCount = 6;
  version = '1.0.0';
  activeTab = { position: 'top', index: 0 };
  localizedGreeting: string = '';

  // Example: Simulate selecting a subscription and navigating to loader
  selectSubscriptionAndGoToLoader(subscriptionId: string) {
    localStorage.setItem('SubscriptionID', subscriptionId);
    // Navigate to loader with sub query param
    window.location.href = `/loading?sub=${subscriptionId}`;
    // If you want to use Angular Router instead, inject Router and use:
    // this.router.navigate(['/loading'], { queryParams: { sub: subscriptionId } });
  }


  ngOnInit(): void {
    this.localizedGreeting = `Welcome, ${this.userName} to the localized home page!`;
    console.log('[Localized Greeting]:', this.localizedGreeting);
  }

}
