import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, ActivatedRoute } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { NavigationModule } from 'cax-design-system/navigation';
import { AuthService } from '@auth0/auth0-angular';
import { HttpClient } from '@angular/common/http';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    NavigationModule,

  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  templateUrl: './home.component.html',
  styleUrls: ['./home.component.scss']
})
export class HomeComponent implements OnInit {

  // Navigation data
  topNavList = [
    { label: 'Dashboard', icon: 'cax-dashboard' },
    { label: 'Products', icon: 'cax-widget' },
    { label: 'Analytics', icon: 'cax-chart' },
    { label: 'Settings', icon: 'cax-settings' }
  ];

  bottomNavList = [
    { label: 'Help', icon: 'cax-help' },
    { label: 'Profile', icon: 'cax-user' }
  ];

  // User data
  userName = 'Justin Kenter';
  userImage = 'https://primefaces.org/cdn/primeng/images/demo/avatar/walter.jpg';
  notificationCount = 6;
  version = '1.0.0';
  activeTab = { position: 'top', index: 0 };
  localizedGreeting: string = '';

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private auth: AuthService,
    private http: HttpClient
  ) {
    console.log('🏡 [HomeComponent] Constructor called');
  }

  // Example: Simulate selecting a subscription and navigating to loader
  selectSubscriptionAndGoToLoader(subscriptionId: string) {
    console.log('🏡 [HomeComponent] selectSubscriptionAndGoToLoader called with:', subscriptionId);
    localStorage.setItem('SubscriptionID', subscriptionId);
    // Navigate to loader with sub query param
    console.log('🏡 [HomeComponent] Navigating to loading with sub param');
    this.router.navigate(['/loading'], { queryParams: { sub: subscriptionId } });
  }

  ngOnInit(): void {
    console.log('🏡 [HomeComponent] ngOnInit called');
    console.log('🏡 [HomeComponent] Current URL:', window.location.href);
    console.log('🏡 [HomeComponent] Router URL:', this.router.url);

    // Get query parameters
    this.activatedRoute.queryParams.subscribe(params => {
      console.log('🏡 [HomeComponent] Query params:', params);
    });

    // Check Auth0 state
    this.auth.isAuthenticated$.subscribe(isAuthenticated => {
      console.log('🏡 [HomeComponent] Auth0 isAuthenticated:', isAuthenticated);
    });

    this.auth.user$.subscribe(user => {
      console.log('🏡 [HomeComponent] Auth0 user:', user);
      if (user) {
        this.userName = user.name || user.nickname || 'User';
        console.log('🏡 [HomeComponent] Updated userName from Auth0:', this.userName);
      }
    });

    // Check localStorage
    const storedUser = localStorage.getItem('user');
    const storedSubId = localStorage.getItem('SubscriptionID');
    console.log('🏡 [HomeComponent] localStorage user:', storedUser);
    console.log('🏡 [HomeComponent] localStorage SubscriptionID:', storedSubId);

    this.localizedGreeting = `Welcome, ${this.userName} to the localized home page!`;
    console.log('🏡 [HomeComponent] Localized Greeting:', this.localizedGreeting);

    // Test API call to verify token flow
    this.testApiCall();

    console.log('🏡 [HomeComponent] ngOnInit completed');
  }

  private testApiCall(): void {
    const storedSubId = localStorage.getItem('SubscriptionID');
    if (storedSubId) {
      console.log('🏡 [HomeComponent] Testing API call with subscription ID:', storedSubId);

      // Test the /me endpoint
      this.http.get(`/api/subscriptions/${storedSubId}/me`).subscribe({
        next: (response) => {
          console.log('🏡 [HomeComponent] ✅ API call successful:', response);
        },
        error: (error) => {
          console.error('🏡 [HomeComponent] ❌ API call failed:', error);
          console.error('🏡 [HomeComponent] Error status:', error.status);
          console.error('🏡 [HomeComponent] Error message:', error.message);
        }
      });
    } else {
      console.log('🏡 [HomeComponent] No subscription ID available for API test');
    }
  }

}
