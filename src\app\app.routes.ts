import { Routes } from '@angular/router';
import { AuthGuard } from '@auth0/auth0-angular';
import { AppAuthGuard } from './_guards/auth.guard';
import { HomeComponent } from './home/<USER>';
import { LoadingComponent } from './loading/loading.component';

export const routes: Routes = [
  {
    path: 'home',
    component: HomeComponent,
    data: { title: $localize`Home` },
    canActivate: [AppAuthGuard],
  },
  {
    path: 'loading',
    component: LoadingComponent,
    data: { title: $localize`Loading` },
    canActivate: [AuthGuard],
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'home',
  },
];

console.log('🗺️ [app.routes.ts] Routes configured:', routes);
