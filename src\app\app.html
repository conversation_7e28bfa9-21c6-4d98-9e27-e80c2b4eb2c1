<div class="app-wrapper" (window:resize)="onResize($event)">
  <app-loading *ngIf="auth.isLoading$ | async; else loaded"></app-loading>
  <ng-template #loaded>
    <div *ngIf="!isLowerResolution">
      <!-- Only HomeComponent for demonstration -->
      <router-outlet></router-outlet>
    </div>
    <div class="resolutionDevice" *ngIf="isLowerResolution">
      <h1>
        <span>Not Supported for Current resolution</span>
      </h1>
    </div>
  </ng-template>
</div>
