import { HttpInterceptorFn, HttpRequest, HttpHandlerFn, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { inject } from '@angular/core';
import { Observable, throwError, switchMap, catchError } from 'rxjs';
import { AuthService } from '@auth0/auth0-angular';
import { Router } from '@angular/router';
import { Auth0Service } from '../../services/auth0.service';

export const authInterceptor: HttpInterceptorFn = (
  req: HttpRequest<unknown>,
  next: HttpHandlerFn
): Observable<HttpEvent<unknown>> => {
  const auth = inject(AuthService);
  const router = inject(Router);
  const auth0Service = inject(Auth0Service);

  console.log('🔐 [AuthInterceptor] Intercepting request:', req.method, req.url);
  console.log('🔐 [AuthInterceptor] Request headers:', req.headers.keys());

  // Always set withCredentials for API requests
  let authReq = req.clone({
    setHeaders: {
      'Content-Type': 'application/json',
      'Accept': 'application/json'
    },
    withCredentials: true
  });

  // If Content-Range header is present, preserve it
  if (req.headers.has('Content-Range')) {
    const contentRange = req.headers.get('Content-Range') || '';
    console.log('🔐 [AuthInterceptor] Preserving Content-Range header:', contentRange);
    authReq = authReq.clone({
      setHeaders: {
        'Content-Range': contentRange
      }
    });
  }

  // Only add Auth0 token for API requests
  if (req.url.includes('/api/')) {
    console.log('🔐 [AuthInterceptor] API request detected, getting Auth0 token');
    return auth.getAccessTokenSilently().pipe(
      switchMap((token: string) => {
        console.log('🔐 [AuthInterceptor] Token obtained successfully');
        console.log('🔐 [AuthInterceptor] Token length:', token?.length);
        console.log('🔐 [AuthInterceptor] Token preview:', token?.substring(0, 50) + '...');

        // Clone the request and add the authorization header
        const tokenReq = authReq.clone({
          setHeaders: {
            Authorization: `Bearer ${token}`
          }
        });

        console.log('🔐 [AuthInterceptor] Proceeding with authenticated request');
        return next(tokenReq);
      }),
      catchError((error: HttpErrorResponse) => {
        console.error('🔐 [AuthInterceptor] Auth interceptor error:', error);
        console.error('🔐 [AuthInterceptor] Error status:', error.status);
        console.error('🔐 [AuthInterceptor] Error message:', error.message);

        // Handle offline scenario
        if (!navigator.onLine) {
          console.log('🔐 [AuthInterceptor] Offline detected, redirecting');
          router.navigate(['/offline'], {
            queryParams: { from: router.url },
          });
        }

        // Handle authentication errors
        if (error.status === 403 || error.status === 401) {
          console.log('🔐 [AuthInterceptor] Authentication error, logging out user');
          localStorage.clear();
          auth0Service.logUserOut();
        }

        return throwError(() => error);
      })
    );
  }

  // For non-API requests, proceed without token
  console.log('🔐 [AuthInterceptor] Non-API request, proceeding without token');
  return next(authReq).pipe(
    catchError((error: HttpErrorResponse) => {
      console.error('🔐 [AuthInterceptor] Non-API request error:', error);
      console.error('🔐 [AuthInterceptor] Non-API error status:', error.status);

      if (!navigator.onLine) {
        console.log('🔐 [AuthInterceptor] Offline detected for non-API request');
        router.navigate(['/offline'], {
          queryParams: { from: router.url },
        });
      }

      if (error.status === 403 || error.status === 401) {
        console.log('🔐 [AuthInterceptor] Auth error on non-API request, logging out');
        localStorage.clear();
        auth0Service.logUserOut();
      }

      return throwError(() => error);
    })
  );
};
