import { ApplicationConfig, provideZoneChangeDetection } from '@angular/core';
import { provideRouter } from '@angular/router';
import { provideAuth0 } from '@auth0/auth0-angular';
import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { environment } from '../environments/environment';
import { authInterceptor } from './_interceptors/auth.interceptor';

import { routes } from './app.routes';

console.log('⚙️ [app.config.ts] Configuring application');
console.log('⚙️ [app.config.ts] Environment:', environment);
console.log('⚙️ [app.config.ts] Auth0 config:', {
  domain: environment.auth0.domain,
  clientId: environment.auth0.clientId,
  callbackURL: environment.auth0.callbackURL,
  audience: environment.auth0.audience,
  useRefreshTokens: environment.auth0.useRefreshTokens
});

export const appConfig: ApplicationConfig = {
  providers: [
    provideZoneChangeDetection({ eventCoalescing: true }),
    provideRouter(routes),
    provideHttpClient(withInterceptors([authInterceptor])),
    provideAuth0({
      domain: environment.auth0.domain,
      clientId: environment.auth0.clientId,
      authorizationParams: {
        redirect_uri: environment.auth0.callbackURL,
        audience: environment.auth0.audience,
      },
      useRefreshTokens: environment.auth0.useRefreshTokens,
    })
  ]
};
