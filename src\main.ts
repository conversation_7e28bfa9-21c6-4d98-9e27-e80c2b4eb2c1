import { bootstrapApplication } from '@angular/platform-browser';
import { appConfig } from './app/app.config';
import { AppComponent } from './app/app';

console.log('🚀 [main.ts] Starting application bootstrap');
console.log('🚀 [main.ts] App config:', appConfig);

bootstrapApplication(AppComponent, appConfig)
  .then(() => {
    console.log('✅ [main.ts] Application bootstrapped successfully');
  })
  .catch((err) => {
    console.error('❌ [main.ts] Bootstrap error:', err);
  });
