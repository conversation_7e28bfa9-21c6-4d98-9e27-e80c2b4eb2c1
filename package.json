{"name": "datax-v2", "version": "1.0.0", "scripts": {"ng": "ng", "start": "ng serve --host m2.app-local.datax.ai --port 3000 --ssl true --proxy-config proxy.conf.json", "build": "ng build", "postbuild": "bash ./build-utils/post-build.sh", "build-dev": "ng build --configuration=dev && npm run postbuild", "build-staging": "ng build --configuration=stag && npm run postbuild", "build-prod": "ng build --configuration=production && npm run postbuild", "watch": "ng build --watch --configuration development", "build:stats": "ng build --stats-json", "analyze": "webpack-bundle-analyzer dist/stats.json", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "^19.2.14", "@angular/cdk": "^19.0.0", "@angular/common": "^19.0.0", "@angular/compiler": "^19.0.0", "@angular/core": "^19.0.0", "@angular/forms": "^19.0.0", "@angular/localize": "^19.2.14", "@angular/material": "^19.0.0", "@angular/platform-browser": "^19.0.0", "@angular/platform-browser-dynamic": "^19.0.0", "@angular/router": "^19.0.0", "@auth0/auth0-angular": "^2.2.3", "@ngxmc/datetime-picker": "^20.0.0", "cax-design-system": "^2.7.5", "chart.js": "^4.4.1", "moment": "^2.30.1", "ngx-dropzone": "^3.0.0", "ngx-infinite-scroll": "^17.0.0", "ngx-pinch-zoom": "^2.6.2", "ngx-quill": "^17.0.0", "quill-mention": "^3.1.0", "rxjs": "^7.8.0", "simple-color-picker": "^1.0.5", "tslib": "^2.8.1", "zone.js": "^0.14.3"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.15", "@angular/cli": "^19.0.0", "@angular/compiler-cli": "^19.0.0", "@types/jasmine": "~5.1.0", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "typescript": "~5.8.2", "webpack-bundle-analyzer": "^4.10.1"}}