// NOTE: This file uses $localize for i18n. Ensure you have @angular/localize installed and import '@angular/localize/init' in your main.ts.
import { Injectable } from '@angular/core';
import {
  CanActivate,
  ActivatedRouteSnapshot,
  RouterStateSnapshot,
  Router,
} from '@angular/router';
import { Observable } from 'rxjs';
// import { PermissionsService } from '../services/permissions.service';
import { AuthService } from '@auth0/auth0-angular';
import { Auth0Service } from '../../services/auth0.service';
import { UserService } from '../../services/user.service';
import { environment } from '../../environments/environment';
import '@angular/localize/init';

@Injectable({
  providedIn: 'root',
})
export class AppAuthGuard implements CanActivate {
  subscriptionId: string | null | undefined;
  user: any;

  constructor(
    private router: Router,
    // private snackBar: MatSnackBar,
    public auth: AuthService,
    public auth0: Auth0Service,
    // public permissionService: PermissionsService,
    public userService: UserService
  ) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Observable<boolean> | Promise<boolean> | boolean {

    // Simple authentication check
    return new Promise<boolean>((resolve) => {
      this.auth.isAuthenticated$.subscribe((isAuthenticated) => {
        if (!isAuthenticated) {
          console.log('User not authenticated, blocking access');
          resolve(false);
          return;
        }

        console.log('User is authenticated, checking subscription...');
        this.user = JSON.parse(localStorage.getItem('user') || 'null');
        this.subscriptionId = route.queryParams['sub'];

    // If subscription ID is not in URL, try to get it from localStorage
    if (!this.subscriptionId) {
      this.subscriptionId = localStorage.getItem('SubscriptionID');
    }

        // proceed only if subs id present
        if (this.subscriptionId) {
          console.log('Subscription ID found:', this.subscriptionId);
          localStorage.setItem('SubscriptionID', this.subscriptionId);
          // Skip API permission check for now - just allow access
          console.log('Skipping permission check - allowing access to home page');
          resolve(true);
        } else {
          // No subscription ID - redirect to loading without showing error
          console.log('No subscription ID found, redirecting to loading');
          this.router.navigate(['/loading'], { queryParams: route.queryParams });
          resolve(false);
        }
      });
    });
  }

  /***
   * Reject User Auth
   */
  rejectAuthentication = (errResp: string) => {
    this.auth0.logUserOut();
    // this.snackBar.open(errResp, 'OK', {
    //   duration: 3000,
    //   panelClass: ['error-snackbar'],
    // });
    alert(errResp);
  };

  /**
   * Redirect user when no module subscription found
   */
  redirectUser = (): void => {
    this.user = JSON.parse(localStorage.getItem('user') || 'null');
    if (this.user && this.user.return_to) {
      console.log(this.user.return_to);
      window.location.href = 'https://' + this.user.return_to;
    } else {
      window.location.href = 'https://' + environment.default_return_url;
    }
  };
}
