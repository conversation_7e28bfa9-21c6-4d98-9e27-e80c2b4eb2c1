<div class="home-container">
  <!-- Navigation Header -->
  <cax-navigation
    [header]="'Dashboard'"
    [topNavList]="topNavList"
    [bottomNavList]="bottomNavList"
    [activeTab]="activeTab"
    [version]="version"
    [userName]="userName"
    [userImage]="userImage"
    [notificationCount]="notificationCount"
  ></cax-navigation>

  <!-- Main Content -->
  <div class="main-content">
    <!-- Loading Indicator -->
    <div *ngIf="isLoading" class="loading-container">
      <p>Loading data...</p>
    </div>

    <!-- Dashboard Content -->
    <div *ngIf="!isLoading" class="dashboard-content">
      <!-- Welcome Section -->
      <div class="welcome-section">
        <h2>{{ localizedGreeting }}</h2>
        <p *ngIf="subscriptionId">Subscription ID: {{ subscriptionId }}</p>
      </div>

      <!-- User Profile Section -->
      <div class="profile-section" *ngIf="userProfile">
        <h3>User Profile</h3>
        <div class="profile-info">
          <p><strong>Name:</strong> {{ userProfile.name || userName }}</p>
          <p><strong>Email:</strong> {{ userProfile.email }}</p>
          <p *ngIf="userProfile.theme_class"><strong>Theme:</strong> {{ userProfile.theme_class }}</p>
        </div>
      </div>

      <!-- Product Headers Section -->
      <div class="products-section" *ngIf="productHeaders.length > 0">
        <h3>Product Headers</h3>
        <div class="headers-grid">
          <div *ngFor="let header of productHeaders" class="header-item">
            <span class="header-name">{{ header.name || header.label }}</span>
            <span class="header-type">{{ header.type }}</span>
          </div>
        </div>
      </div>

      <!-- API Usage Section -->
      <div class="api-usage-section" *ngIf="apiUsageData">
        <h3>API Usage</h3>
        <div class="usage-stats">
          <div class="stat-item">
            <span class="stat-label">Total Requests:</span>
            <span class="stat-value">{{ apiUsageData.total_requests || 0 }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Remaining:</span>
            <span class="stat-value">{{ apiUsageData.remaining || 0 }}</span>
          </div>
          <div class="stat-item" *ngIf="apiUsageData.reset_date">
            <span class="stat-label">Reset Date:</span>
            <span class="stat-value">{{ apiUsageData.reset_date | date }}</span>
          </div>
        </div>
      </div>

      <!-- Demo Actions -->
      <div class="demo-actions">
        <h3>Demo Actions</h3>
        <button class="demo-button" (click)="selectSubscriptionAndGoToLoader('D1PEKO7')">
          Go to Loader with Subscription
        </button>
      </div>
    </div>
  </div>
</div>
