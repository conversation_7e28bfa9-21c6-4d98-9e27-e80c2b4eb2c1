<!-- <cax-navigation
  [header]="'Content Management'"
  [topNavList]="[{label: 'Bat<PERSON>', icon: 'cax-widget'}, {label: 'Apps', icon: 'cax-widget'}]"
  [bottomNavList]="[{label: 'Apps', icon: 'cax-widget'}]"
  [activeTab]="'notifications'"
  [version]="'1.0.0'"
  [userName]="'<PERSON>'"
  [userImage]="'https://primefaces.org/cdn/primeng/images/demo/avatar/walter.jpg'"
  [notificationCount]="6"
></cax-navigation> -->

<!-- Demo: Button to simulate selecting a subscription and navigating to loader -->
<cax-button (click)="selectSubscriptionAndGoToLoader('D1PEKO7')">Go to Loader with Subscription</cax-button>

<!-- Localized greeting example -->
<div>
  {{ localizedGreeting }}
</div>
