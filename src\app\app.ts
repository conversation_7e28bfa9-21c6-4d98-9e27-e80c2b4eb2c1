import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AuthService } from '@auth0/auth0-angular';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { NgIf, AsyncPipe } from '@angular/common';
import { LoadingComponent } from './loading/loading.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet, LoadingComponent, NgIf, AsyncPipe],
  templateUrl: './app.html',
  styleUrls: ['./app.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'auth-demo';
  isLowerResolution: boolean | undefined;
  subscriptionId: string | null = null;
  event$: any;
  permissionsAvailable = true; // mock for demo

  constructor(
    public router: Router,
    private activatedRoute: ActivatedRoute,
    public auth: AuthService
  ) {
    console.log('[AppComponent] Constructor');
  }

  ngOnInit() {
    console.log('[AppComponent] ngOnInit');
    // Check for window width and height to show 'resolution unsupported' msg
    this.checkResolution();
    window.addEventListener('resize', this.onResize);
  }

  ngOnDestroy() {
    console.log('[AppComponent] ngOnDestroy');
    window.removeEventListener('resize', this.onResize);
  }

  onResize = (event: any) => {
    this.checkResolution(event?.target?.innerWidth);
  };

  private checkResolution(width?: number) {
    const w = width || window.innerWidth;
    this.isLowerResolution = w < 1280;
  }
}
