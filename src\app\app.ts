import { Component, OnInit, OnDestroy } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter } from 'rxjs/operators';
import { AuthService } from '@auth0/auth0-angular';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { NgIf, AsyncPipe } from '@angular/common';
import { LoadingComponent } from './loading/loading.component';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, RouterOutlet, LoadingComponent, NgIf, AsyncPipe],
  templateUrl: './app.html',
  styleUrls: ['./app.scss']
})
export class AppComponent implements OnInit, OnDestroy {
  title = 'auth-demo';
  isLowerResolution: boolean | undefined;
  subscriptionId: string | null = null;
  event$: any;
  permissionsAvailable = true; // mock for demo

  constructor(
    public router: Router,
    private activatedRoute: ActivatedRoute,
    public auth: AuthService
  ) {
    console.log('🏠 [AppComponent] Constructor called');
    console.log('🏠 [AppComponent] Current URL:', window.location.href);
    console.log('🏠 [AppComponent] Router URL:', this.router.url);

    // Listen to router events
    this.router.events.subscribe(event => {
      console.log('🏠 [AppComponent] Router event:', event.constructor.name, event);
    });
  }

  ngOnInit() {
    console.log('🏠 [AppComponent] ngOnInit called');
    console.log('🏠 [AppComponent] Current route:', this.router.url);

    // Monitor Auth0 state
    this.auth.isLoading$.subscribe(isLoading => {
      console.log('🏠 [AppComponent] Auth0 isLoading:', isLoading);
    });

    this.auth.isAuthenticated$.subscribe(isAuthenticated => {
      console.log('🏠 [AppComponent] Auth0 isAuthenticated:', isAuthenticated);
    });

    this.auth.user$.subscribe(user => {
      console.log('🏠 [AppComponent] Auth0 user:', user);
    });

    this.auth.error$.subscribe(error => {
      if (error) {
        console.error('🏠 [AppComponent] Auth0 error:', error);
      }
    });

    // Check for window width and height to show 'resolution unsupported' msg
    this.checkResolution();
    window.addEventListener('resize', this.onResize);

    console.log('🏠 [AppComponent] ngOnInit completed');
  }

  ngOnDestroy() {
    console.log('🏠 [AppComponent] ngOnDestroy called');
    window.removeEventListener('resize', this.onResize);
  }

  onResize = (event: any) => {
    console.log('🏠 [AppComponent] Window resize event:', event?.target?.innerWidth);
    this.checkResolution(event?.target?.innerWidth);
  };

  private checkResolution(width?: number) {
    const w = width || window.innerWidth;
    this.isLowerResolution = w < 1280;
    console.log('🏠 [AppComponent] Resolution check - width:', w, 'isLowerResolution:', this.isLowerResolution);
  }
}
