import { Injectable } from '@angular/core';
import { AuthService } from '@auth0/auth0-angular';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class Auth0Service {
  profileJson: string | undefined;
  constructor(public auth: AuthService) {}

  login = () => {
    this.auth.loginWithRedirect({
      appState: { target: window.location.origin },
    });
  };

  logUserOut = () => {
    let cameFromCookie = document.cookie
      ?.split(';')
      ?.filter((el) => el.includes('came_from='))[0]
      ?.split('came_from=')[1];
    console.log('cameFromCookie' + cameFromCookie);
    console.log(typeof cameFromCookie);
    let returnPathFromCookie =
      cameFromCookie && cameFromCookie != 'undefined'
        ? 'https://' + cameFromCookie
        : environment.default_return_url;
    console.log(returnPathFromCookie);
    localStorage.clear();
    this.auth.logout({
      logoutParams: { returnTo: returnPathFromCookie },
    });
  };

  getUsers = () => {
    this.auth.user$.subscribe((profile) => {
      this.profileJson = JSON.stringify(profile, null, 2);
    });
    // this.auth.getAccessTokenSilently().subscribe((token) => console.log(token));
    // this.auth.idTokenClaims$.subscribe((claims) => console.log(claims));
  };
}
