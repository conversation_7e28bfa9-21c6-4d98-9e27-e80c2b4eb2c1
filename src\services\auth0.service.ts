import { Injectable } from '@angular/core';
import { AuthService } from '@auth0/auth0-angular';
import { environment } from '../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class Auth0Service {
  profileJson: string | undefined;
  constructor(public auth: AuthService) {}

  login = () => {
    console.log('🔑 [Auth0Service] login() called');
    console.log('🔑 [Auth0Service] Current location:', window.location.href);
    console.log('🔑 [Auth0Service] Target origin:', window.location.origin);

    this.auth.loginWithRedirect({
      appState: { target: window.location.origin },
    });
  };

  logUserOut = () => {
    console.log('🔑 [Auth0Service] logUserOut() called');
    console.log('🔑 [Auth0Service] Current cookies:', document.cookie);

    let cameFromCookie = document.cookie
      ?.split(';')
      ?.filter((el) => el.includes('came_from='))[0]
      ?.split('came_from=')[1];
    console.log('🔑 [Auth0Service] cameFromCookie:', cameFromCookie);
    console.log('🔑 [Auth0Service] cameFromCookie type:', typeof cameFromCookie);

    let returnPathFromCookie =
      cameFromCookie && cameFromCookie != 'undefined'
        ? 'https://' + cameFromCookie
        : environment.default_return_url;
    console.log('🔑 [Auth0Service] returnPathFromCookie:', returnPathFromCookie);
    console.log('🔑 [Auth0Service] Clearing localStorage');

    localStorage.clear();

    console.log('🔑 [Auth0Service] Calling Auth0 logout');
    this.auth.logout({
      logoutParams: { returnTo: returnPathFromCookie },
    });
  };

  getUsers = () => {
    console.log('🔑 [Auth0Service] getUsers() called');
    this.auth.user$.subscribe((profile) => {
      console.log('🔑 [Auth0Service] User profile received:', profile);
      this.profileJson = JSON.stringify(profile, null, 2);
      console.log('🔑 [Auth0Service] Profile JSON:', this.profileJson);
    });

    // Uncommented for debugging
    this.auth.getAccessTokenSilently().subscribe((token) => {
      console.log('🔑 [Auth0Service] Access token:', token?.substring(0, 50) + '...');
    });

    this.auth.idTokenClaims$.subscribe((claims) => {
      console.log('🔑 [Auth0Service] ID token claims:', claims);
    });
  };
}
