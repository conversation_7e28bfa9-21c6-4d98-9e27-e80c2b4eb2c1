/* API Testing Dashboard Styles */

.home-container {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  h1 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 2.5rem;
  }

  p {
    color: #6c757d;
    margin-bottom: 2rem;
    font-size: 1.1rem;
  }

  .api-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #ffffff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    h2 {
      color: #495057;
      margin-bottom: 1rem;
      font-size: 1.5rem;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 0.5rem;
    }

    .button-group {
      display: flex;
      flex-wrap: wrap;
      gap: 0.75rem;

      cax-button {
        flex: 1;
        min-width: 200px;
        margin: 0;
      }
    }
  }

  .api-response-section {
    margin-top: 2rem;
    padding: 1.5rem;
    background-color: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);

    h2 {
      color: #495057;
      margin-bottom: 1rem;
      font-size: 1.5rem;
    }

    .response-info {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding: 0.75rem;
      background-color: #ffffff;
      border-radius: 6px;
      border: 1px solid #dee2e6;

      .last-call {
        font-weight: 600;
        color: #495057;
      }

      .response-status {
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 600;

        &.success {
          background-color: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }

        &.success-parsed {
          background-color: #fff3cd;
          color: #856404;
          border: 1px solid #ffeaa7;
        }

        &.error {
          background-color: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }

        &.loading {
          background-color: #cce7ff;
          color: #004085;
          border: 1px solid #b3d7ff;
        }

        &.pending {
          background-color: #e2e3e5;
          color: #383d41;
          border: 1px solid #d6d8db;
        }
      }
    }

    .api-response {
      background-color: #ffffff;
      border: 1px solid #ced4da;
      border-radius: 8px;
      padding: 1.25rem;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
      font-size: 13px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-wrap: break-word;
      max-height: 500px;
      overflow-y: auto;
      margin: 0;
      color: #212529;
    }
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .home-container {
    padding: 1rem;

    .api-section .button-group {
      flex-direction: column;

      cax-button {
        min-width: auto;
      }
    }

    .api-response-section .response-info {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
  }
}
