.home-container {
  padding: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

  .main-content {
    padding: 1rem;
  }

  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    font-size: 1.2rem;
    color: #6c757d;
  }

  .dashboard-content {
    max-width: 1200px;
    margin: 0 auto;
  }

  .welcome-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);

    h2 {
      margin: 0 0 0.5rem 0;
      font-size: 1.8rem;
      font-weight: 600;
    }

    p {
      margin: 0;
      opacity: 0.9;
      font-size: 0.9rem;
    }
  }

  .profile-section,
  .products-section,
  .api-usage-section,
  .demo-actions {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #ffffff;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    h3 {
      color: #495057;
      margin: 0 0 1rem 0;
      font-size: 1.3rem;
      border-bottom: 2px solid #e9ecef;
      padding-bottom: 0.5rem;
    }
  }

  .profile-info {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;

    p {
      margin: 0.5rem 0;
      padding: 0.75rem;
      background-color: #f8f9fa;
      border-radius: 6px;
      border-left: 4px solid #007bff;

      strong {
        color: #495057;
      }
    }
  }

  .headers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;

    .header-item {
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #dee2e6;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;

      .header-name {
        font-weight: 600;
        color: #495057;
      }

      .header-type {
        font-size: 0.9rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }

  .usage-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;

    .stat-item {
      padding: 1rem;
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      color: white;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);

      .stat-label {
        display: block;
        font-size: 0.9rem;
        opacity: 0.9;
        margin-bottom: 0.5rem;
      }

      .stat-value {
        display: block;
        font-size: 1.5rem;
        font-weight: 600;
      }
    }
  }

  .demo-button {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }

    &:active {
      transform: translateY(0);
    }
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .home-container {
    padding: 0.5rem;

    .main-content {
      padding: 0.5rem;
    }

    .welcome-section h2 {
      font-size: 1.5rem;
    }

    .profile-info {
      grid-template-columns: 1fr;
    }

    .headers-grid {
      grid-template-columns: 1fr;
    }

    .usage-stats {
      grid-template-columns: 1fr;
    }
  }
}
