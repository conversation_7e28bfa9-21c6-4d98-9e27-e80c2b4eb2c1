{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"auth-demo": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"outputPath": "dist/auth-demo", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js", "@angular/localize/init"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "node_modules/cax-design-system/resources", "output": "/assets/"}], "styles": ["src/styles.scss"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "500kB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "4kB", "maximumError": "8kB"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "auth-demo:build:production"}, "development": {"buildTarget": "auth-demo:build:development"}}, "defaultConfiguration": "development", "options": {"proxyConfig": "proxy.conf.json"}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"outFile": "messages.en.xlf", "outputPath": "src/locale"}}, "test": {"builder": "@angular/build:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": [{"glob": "**/*", "input": "node_modules/cax-design-system/resources", "output": "/assets/"}], "styles": ["src/styles.scss"]}}}}}, "cli": {"analytics": "e6af59c1-456d-4591-b28b-3a79c38edf40"}}